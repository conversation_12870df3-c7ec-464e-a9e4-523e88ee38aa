{"name": "backend", "version": "1.0.0", "description": "", "main": "index.js", "type": "module", "scripts": {"start": "node server.js", "dev": "node server.js"}, "author": "", "license": "ISC", "dependencies": {"bcrypt": "^5.1.1", "cloudinary": "^1.41.2", "cookie-parser": "^1.4.6", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "express-fileupload": "^1.4.3", "jsonwebtoken": "^9.0.2", "mongoose": "^8.0.3", "validator": "^13.11.0"}, "devDependencies": {"nodemon": "^3.1.7"}}